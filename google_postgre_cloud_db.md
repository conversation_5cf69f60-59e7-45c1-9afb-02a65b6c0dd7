<?php
// config.php - Database configuration
$host = '************';
$port = '5432';
$dbname = 'postgres';
$username = 'postgres';
$password = 'YOUR_PASSWORD_HERE'; // Replace with your actual password

try {
    $pdo = new PDO("pgsql:host=$host;port=$port;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>

<?php
// init.php - Initialize database table
require_once 'config.php';

// Create users table if it doesn't exist
$sql = "CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON>HA<PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)";

try {
    $pdo->exec($sql);
    echo "Table 'users' created successfully or already exists.";
} catch(PDOException $e) {
    echo "Error creating table: " . $e->getMessage();
}
?>


<?php
// api.php - CRUD operations API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch($method) {
        case 'GET':
            if(isset($_GET['id'])) {
                // Get single user
                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$_GET['id']]);
                $user = $stmt->fetch();
                echo json_encode($user ?: ['error' => 'User not found']);
            } else {
                // Get all users
                $stmt = $pdo->query("SELECT * FROM users ORDER BY id DESC");
                $users = $stmt->fetchAll();
                echo json_encode($users);
            }
            break;

        case 'POST':
            // Create new user
            if(!isset($input['name']) || !isset($input['email'])) {
                echo json_encode(['error' => 'Name and email are required']);
                break;
            }
            
            $stmt = $pdo->prepare("INSERT INTO users (name, email, phone) VALUES (?, ?, ?) RETURNING id");
            $stmt->execute([$input['name'], $input['email'], $input['phone'] ?? null]);
            $id = $stmt->fetchColumn();
            
            echo json_encode(['success' => true, 'id' => $id, 'message' => 'User created successfully']);
            break;

        case 'PUT':
            // Update user
            if(!isset($input['id'])) {
                echo json_encode(['error' => 'User ID is required']);
                break;
            }
            
            $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ?, phone = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $result = $stmt->execute([$input['name'], $input['email'], $input['phone'], $input['id']]);
            
            if($stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'User updated successfully']);
            } else {
                echo json_encode(['error' => 'User not found or no changes made']);
            }
            break;

        case 'DELETE':
            // Delete user
            if(!isset($_GET['id'])) {
                echo json_encode(['error' => 'User ID is required']);
                break;
            }
            
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $result = $stmt->execute([$_GET['id']]);
            
            if($stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
            } else {
                echo json_encode(['error' => 'User not found']);
            }
            break;

        default:
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch(PDOException $e) {
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PostgreSQL CRUD Application</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .form-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }

        input[type="text"], input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="email"]:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .users-table th {
            background: #374151;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .users-table td {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .users-table tr:hover {
            background: #f9fafb;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .users-table {
                font-size: 14px;
            }
            
            .btn {
                padding: 10px 16px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PostgreSQL CRUD Application</h1>
            <p>Complete Create, Read, Update, Delete operations</p>
        </div>
        
        <div class="content">
            <div id="alert-container"></div>
            
            <div class="form-section">
                <h2 id="form-title">Add New User</h2>
                <form id="user-form">
                    <input type="hidden" id="user-id">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Name *</label>
                            <input type="text" id="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone</label>
                        <input type="text" id="phone">
                    </div>
                    <button type="submit" class="btn btn-primary" id="submit-btn">Add User</button>
                    <button type="button" class="btn btn-secondary" id="cancel-btn" onclick="resetForm()" style="display: none;">Cancel</button>
                </form>
            </div>
            
            <div>
                <h2>Users List</h2>
                <div id="loading" class="loading">Loading users...</div>
                <table class="users-table" id="users-table" style="display: none;">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-tbody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'api.php';
        
        // DOM Elements
        const userForm = document.getElementById('user-form');
        const alertContainer = document.getElementById('alert-container');
        const usersTable = document.getElementById('users-table');
        const usersTbody = document.getElementById('users-tbody');
        const loading = document.getElementById('loading');
        const formTitle = document.getElementById('form-title');
        const submitBtn = document.getElementById('submit-btn');
        const cancelBtn = document.getElementById('cancel-btn');

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
        });

        // Form submission
        userForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const userId = document.getElementById('user-id').value;
            if (userId) {
                updateUser(userId);
            } else {
                createUser();
            }
        });

        // Create user
        async function createUser() {
            const userData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value
            };

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();
                
                if (result.success) {
                    showAlert('User created successfully!', 'success');
                    resetForm();
                    loadUsers();
                } else {
                    showAlert(result.error || 'Error creating user', 'error');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message, 'error');
            }
        }

        // Load all users
        async function loadUsers() {
            try {
                loading.style.display = 'block';
                usersTable.style.display = 'none';

                const response = await fetch(API_URL);
                const users = await response.json();

                loading.style.display = 'none';
                usersTable.style.display = 'table';

                usersTbody.innerHTML = '';

                if (users.length === 0) {
                    usersTbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">No users found</td></tr>';
                    return;
                }

                users.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${user.id}</td>
                        <td>${user.name}</td>
                        <td>${user.email}</td>
                        <td>${user.phone || '-'}</td>
                        <td>${new Date(user.created_at).toLocaleDateString()}</td>
                        <td>
                            <button class="btn btn-success" onclick="editUser(${user.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deleteUser(${user.id})">Delete</button>
                        </td>
                    `;
                    usersTbody.appendChild(row);
                });
            } catch (error) {
                loading.style.display = 'none';
                showAlert('Error loading users: ' + error.message, 'error');
            }
        }

        // Edit user
        async function editUser(id) {
            try {
                const response = await fetch(`${API_URL}?id=${id}`);
                const user = await response.json();

                if (user.error) {
                    showAlert(user.error, 'error');
                    return;
                }

                document.getElementById('user-id').value = user.id;
                document.getElementById('name').value = user.name;
                document.getElementById('email').value = user.email;
                document.getElementById('phone').value = user.phone || '';

                formTitle.textContent = 'Update User';
                submitBtn.textContent = 'Update User';
                cancelBtn.style.display = 'inline-block';

                // Scroll to form
                document.querySelector('.form-section').scrollIntoView({ behavior: 'smooth' });
            } catch (error) {
                showAlert('Error loading user: ' + error.message, 'error');
            }
        }

        // Update user
        async function updateUser(id) {
            const userData = {
                id: parseInt(id),
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value
            };

            try {
                const response = await fetch(API_URL, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();
                
                if (result.success) {
                    showAlert('User updated successfully!', 'success');
                    resetForm();
                    loadUsers();
                } else {
                    showAlert(result.error || 'Error updating user', 'error');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message, 'error');
            }
        }

        // Delete user
        async function deleteUser(id) {
            if (!confirm('Are you sure you want to delete this user?')) {
                return;
            }

            try {
                const response = await fetch(`${API_URL}?id=${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                
                if (result.success) {
                    showAlert('User deleted successfully!', 'success');
                    loadUsers();
                } else {
                    showAlert(result.error || 'Error deleting user', 'error');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message, 'error');
            }
        }

        // Reset form
        function resetForm() {
            userForm.reset();
            document.getElementById('user-id').value = '';
            formTitle.textContent = 'Add New User';
            submitBtn.textContent = 'Add User';
            cancelBtn.style.display = 'none';
        }

        // Show alert
        function showAlert(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // Auto hide after 5 seconds
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }
    </script>
</body>
</html>


I'll create a complete CRUD application using HTML, JavaScript, and PHP to interact with your PostgreSQL database.## Complete CRUD Application Files

I've created a complete CRUD application with the following files:

### 1. **config.php** - Database Configuration
- Contains connection settings for your PostgreSQL database
- **Important:** Replace `YOUR_PASSWORD_HERE` with the actual password you set for your postgres user

### 2. **init.php** - Database Initialization
- Creates the `users` table if it doesn't exist
- Run this file once to set up your database structure

### 3. **api.php** - CRUD API Backend
- Handles all CRUD operations (Create, Read, Update, Delete)
- RESTful API endpoints:
  - `GET` - Retrieve all users or single user
  - `POST` - Create new user
  - `PUT` - Update existing user
  - `DELETE` - Delete user

### 4. **index.html** - Frontend Interface
- Modern, responsive web interface
- Real-time CRUD operations without page refresh
- Form validation and error handling
- Beautiful gradient design with animations

## Setup Instructions

### 1. **Set Database Password**
First, set a password for your postgres user in Google Cloud Console:
1. Go to Cloud SQL → dcbuyer-db → Users
2. Click on `postgres` user
3. Set a password

### 2. **Deploy Files**
Upload all files to your web server or local development environment with PHP support.

### 3. **Update Configuration**
Edit `config.php` and replace `YOUR_PASSWORD_HERE` with your actual database password.

### 4. **Initialize Database**
Run `init.php` once to create the users table:
```
http://your-domain.com/init.php
```

### 5. **Access Application**
Open `index.html` in your browser:
```
http://your-domain.com/index.html
```

## Features

✅ **Create** - Add new users with name, email, and phone  
✅ **Read** - View all users in a responsive table  
✅ **Update** - Edit existing users inline  
✅ **Delete** - Remove users with confirmation  
✅ **Validation** - Client and server-side validation  
✅ **Error Handling** - Comprehensive error messages  
✅ **Responsive Design** - Works on mobile and desktop  
✅ **Real-time Updates** - No page refresh needed  

## Security Considerations

For production use, consider:
1. Enable SSL connections in your database
2. Add IP restrictions
3. Implement authentication
4. Add input sanitization
5. Use environment variables for credentials

The application is ready to use and will work with your PostgreSQL database once you set the password!


Based on your Google Cloud SQL PostgreSQL database information, here are the connection details and instructions:

## Database Connection Information

**Connection Details:**
- **Host/IP Address:** ************
- **Port:** 5432 (default PostgreSQL port)
- **Database Name:** postgres
- **Username:** postgres
- **Instance Name:** dcbuyer-db
- **PostgreSQL Version:** 17

## Connection Methods

### 1. **Google Cloud Shell (Recommended for beginners)**
- Click "Open Cloud Shell" button in your Google Cloud Console
- Use the gcloud command:
```bash
gcloud sql connect dcbuyer-db --user=postgres
```

### 2. **Direct Connection via psql (Command Line)**
```bash
psql -h ************ -p 5432 -U postgres -d postgres
```

### 3. **Application Connection String**
```
postgresql://postgres:[PASSWORD]@************:5432/postgres
```

### 4. **GUI Database Tools**
You can use tools like:
- pgAdmin
- DBeaver
- TablePlus
- DataGrip

**Connection settings for GUI tools:**
- Host: ************
- Port: 5432
- Database: postgres
- Username: postgres
- Password: [You'll need to set this]

## Important Setup Steps

### 1. **Set Password for postgres user**
You need to set a password for the postgres user first:
1. Go to your Cloud SQL instance
2. Click on "Users" tab
3. Click on the postgres user
4. Set a password

### 2. **Security Considerations**
Your database currently has:
- **Public IP enabled** (accessible from internet)
- **SSL connections NOT required** (consider enabling for security)

**Recommended:** Enable "Require SSL connections" in the Security tab for production use.

### 3. **Firewall/IP Allowlist**
Make sure your IP address is allowed to connect. You may need to:
1. Go to "Connections" → "Networking"
2. Add your public IP to authorized networks

## Storing Data

Once connected, you can:

### Create a Database
```sql
CREATE DATABASE your_app_db;
```

### Create Tables
```sql
\c your_app_db  -- Connect to your new database
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Insert Data
```sql
INSERT INTO users (name, email) VALUES ('John Doe', '<EMAIL>');
```

## Next Steps

1. **Set up the postgres user password**
2. **Test connection using Google Cloud Shell first**
3. **Create your application database**
4. **Set up proper security (SSL, IP restrictions)**
5. **Connect your application using the connection string**

The database is already running and ready to accept connections once you set up the password and configure access permissions.