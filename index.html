<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PostgreSQL CRUD Application</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .form-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #374151;
        }

        input[type="text"], input[type="email"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus, input[type="email"]:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-2px);
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .users-table th {
            background: #374151;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }

        .users-table td {
            padding: 15px;
            border-bottom: 1px solid #e5e7eb;
        }

        .users-table tr:hover {
            background: #f9fafb;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .users-table {
                font-size: 14px;
            }
            
            .btn {
                padding: 10px 16px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PostgreSQL CRUD Application</h1>
            <p>Complete Create, Read, Update, Delete operations</p>
        </div>
        
        <div class="content">
            <div id="alert-container"></div>
            
            <div class="form-section">
                <h2 id="form-title">Add New User</h2>
                <form id="user-form">
                    <input type="hidden" id="user-id">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">Name *</label>
                            <input type="text" id="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">Email *</label>
                            <input type="email" id="email" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone</label>
                        <input type="text" id="phone">
                    </div>
                    <button type="submit" class="btn btn-primary" id="submit-btn">Add User</button>
                    <button type="button" class="btn btn-secondary" id="cancel-btn" onclick="resetForm()" style="display: none;">Cancel</button>
                </form>
            </div>
            
            <div>
                <h2>Users List</h2>
                <div id="loading" class="loading">Loading users...</div>
                <table class="users-table" id="users-table" style="display: none;">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="users-tbody">
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        const API_URL = 'api.php';
        
        // DOM Elements
        const userForm = document.getElementById('user-form');
        const alertContainer = document.getElementById('alert-container');
        const usersTable = document.getElementById('users-table');
        const usersTbody = document.getElementById('users-tbody');
        const loading = document.getElementById('loading');
        const formTitle = document.getElementById('form-title');
        const submitBtn = document.getElementById('submit-btn');
        const cancelBtn = document.getElementById('cancel-btn');

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
        });

        // Form submission
        userForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const userId = document.getElementById('user-id').value;
            if (userId) {
                updateUser(userId);
            } else {
                createUser();
            }
        });

        // Create user
        async function createUser() {
            const userData = {
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value
            };

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('User created successfully!', 'success');
                    resetForm();
                    loadUsers();
                } else {
                    showAlert(result.error || 'Error creating user', 'error');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message, 'error');
            }
        }

        // Load all users
        async function loadUsers() {
            try {
                loading.style.display = 'block';
                usersTable.style.display = 'none';

                const response = await fetch(API_URL);
                const users = await response.json();

                loading.style.display = 'none';
                usersTable.style.display = 'table';

                usersTbody.innerHTML = '';

                if (users.length === 0) {
                    usersTbody.innerHTML = '<tr><td colspan="6" style="text-align: center;">No users found</td></tr>';
                    return;
                }

                users.forEach(user => {
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${user.id}</td>
                        <td>${user.name}</td>
                        <td>${user.email}</td>
                        <td>${user.phone || '-'}</td>
                        <td>${new Date(user.created_at).toLocaleDateString()}</td>
                        <td>
                            <button class="btn btn-success" onclick="editUser(${user.id})">Edit</button>
                            <button class="btn btn-danger" onclick="deleteUser(${user.id})">Delete</button>
                        </td>
                    `;
                    usersTbody.appendChild(row);
                });
            } catch (error) {
                loading.style.display = 'none';
                showAlert('Error loading users: ' + error.message, 'error');
            }
        }

        // Edit user
        async function editUser(id) {
            try {
                const response = await fetch(`${API_URL}?id=${id}`);
                const user = await response.json();

                if (user.error) {
                    showAlert(user.error, 'error');
                    return;
                }

                document.getElementById('user-id').value = user.id;
                document.getElementById('name').value = user.name;
                document.getElementById('email').value = user.email;
                document.getElementById('phone').value = user.phone || '';

                formTitle.textContent = 'Update User';
                submitBtn.textContent = 'Update User';
                cancelBtn.style.display = 'inline-block';

                // Scroll to form
                document.querySelector('.form-section').scrollIntoView({ behavior: 'smooth' });
            } catch (error) {
                showAlert('Error loading user: ' + error.message, 'error');
            }
        }

        // Update user
        async function updateUser(id) {
            const userData = {
                id: parseInt(id),
                name: document.getElementById('name').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value
            };

            try {
                const response = await fetch(API_URL, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userData)
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('User updated successfully!', 'success');
                    resetForm();
                    loadUsers();
                } else {
                    showAlert(result.error || 'Error updating user', 'error');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message, 'error');
            }
        }

        // Delete user
        async function deleteUser(id) {
            if (!confirm('Are you sure you want to delete this user?')) {
                return;
            }

            try {
                const response = await fetch(`${API_URL}?id=${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('User deleted successfully!', 'success');
                    loadUsers();
                } else {
                    showAlert(result.error || 'Error deleting user', 'error');
                }
            } catch (error) {
                showAlert('Network error: ' + error.message, 'error');
            }
        }

        // Reset form
        function resetForm() {
            userForm.reset();
            document.getElementById('user-id').value = '';
            formTitle.textContent = 'Add New User';
            submitBtn.textContent = 'Add User';
            cancelBtn.style.display = 'none';
        }

        // Show alert
        function showAlert(message, type) {
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // Auto hide after 5 seconds
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 5000);
        }
    </script>
</body>
</html>
