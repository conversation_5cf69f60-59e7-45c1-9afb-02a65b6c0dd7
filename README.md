# PostgreSQL CRUD Application

A complete CRUD (Create, Read, Update, Delete) application using HTML, JavaScript, and PHP to interact with your Google Cloud PostgreSQL database.

## Files Overview

- **config.php** - Database configuration and connection
- **init.php** - Database table initialization
- **api.php** - RESTful API backend for CRUD operations
- **index.html** - Frontend user interface
- **README.md** - This documentation file

## Database Information

**Your Google Cloud PostgreSQL Database:**
- **Host:** ************
- **Port:** 5432
- **Database:** postgres
- **Username:** postgres
- **Instance:** dcbuyer-db

## Setup Instructions

### 1. Set Database Password

First, you need to set a password for your postgres user:

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to SQL → dcbuyer-db → Users
3. Click on the `postgres` user
4. Set a strong password
5. Save the changes

### 2. Update Configuration

Edit `config.php` and replace `YOUR_PASSWORD_HERE` with the actual password you just set:

```php
$password = 'your_actual_password_here';
```

### 3. Deploy Files

Upload all files to your web server or XAMPP htdocs folder:
- For XAMPP: Place files in `c:\xampp\htdocs\your_project_folder\`
- For web server: Upload to your domain's public folder

### 4. Initialize Database

Run the initialization script once to create the users table:

**For XAMPP:**
```
http://localhost/your_project_folder/init.php
```

**For web server:**
```
http://your-domain.com/init.php
```

You should see: "Table 'users' created successfully or already exists."

### 5. Access Application

Open the main application:

**For XAMPP:**
```
http://localhost/your_project_folder/index.html
```

**For web server:**
```
http://your-domain.com/index.html
```

## Features

✅ **Create Users** - Add new users with name, email, and phone  
✅ **Read Users** - View all users in a responsive table  
✅ **Update Users** - Edit existing users inline  
✅ **Delete Users** - Remove users with confirmation dialog  
✅ **Form Validation** - Client and server-side validation  
✅ **Error Handling** - Comprehensive error messages  
✅ **Responsive Design** - Works on mobile and desktop  
✅ **Real-time Updates** - No page refresh needed  

## API Endpoints

The `api.php` file provides RESTful endpoints:

- **GET** `/api.php` - Get all users
- **GET** `/api.php?id=1` - Get specific user
- **POST** `/api.php` - Create new user
- **PUT** `/api.php` - Update existing user
- **DELETE** `/api.php?id=1` - Delete user

## Database Schema

The application creates a `users` table with the following structure:

```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Testing the Application

1. **Add a User:**
   - Fill in the form with name and email (required)
   - Phone is optional
   - Click "Add User"

2. **Edit a User:**
   - Click "Edit" button next to any user
   - Modify the information
   - Click "Update User"

3. **Delete a User:**
   - Click "Delete" button next to any user
   - Confirm the deletion in the popup

## Troubleshooting

### Connection Issues

1. **"Connection failed" error:**
   - Check if you've set the correct password in `config.php`
   - Verify your IP is allowed in Google Cloud SQL networking settings

2. **"Table doesn't exist" error:**
   - Make sure you've run `init.php` first

3. **"Permission denied" error:**
   - Check if the postgres user has proper permissions
   - Verify the database name is correct

### Security Considerations

For production use, consider:

1. **Enable SSL connections** in Google Cloud SQL
2. **Add IP restrictions** to limit access
3. **Implement user authentication** for the web interface
4. **Add input sanitization** and validation
5. **Use environment variables** for database credentials
6. **Enable HTTPS** for your web application

## Support

If you encounter any issues:

1. Check the browser console for JavaScript errors
2. Check your web server error logs
3. Verify database connectivity using Google Cloud Shell:
   ```bash
   gcloud sql connect dcbuyer-db --user=postgres
   ```

The application is ready to use once you complete the setup steps!
