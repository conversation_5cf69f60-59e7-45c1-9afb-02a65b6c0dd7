<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todos CRUD - Supabase</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .content {
            padding: 30px;
        }

        .form-section {
            background: #f8fafc;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }

        input[type="text"], textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input:focus, textarea:focus {
            outline: none;
            border-color: #10b981;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 20px;
            height: 20px;
            accent-color: #10b981;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: #10b981;
            color: white;
        }

        .btn-primary:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-success {
            background: #22c55e;
            color: white;
        }

        .btn-warning {
            background: #f59e0b;
            color: white;
        }

        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }

        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }

        .todos-grid {
            display: grid;
            gap: 15px;
            margin-top: 20px;
        }

        .todo-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s;
            position: relative;
        }

        .todo-card:hover {
            border-color: #10b981;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.1);
        }

        .todo-card.completed {
            background: #f0fdf4;
            border-color: #22c55e;
        }

        .todo-task {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #1f2937;
        }

        .todo-card.completed .todo-task {
            text-decoration: line-through;
            color: #6b7280;
        }

        .todo-meta {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 15px;
        }

        .todo-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }

        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
            font-size: 18px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6b7280;
        }

        .empty-state h3 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .filter-section {
            background: #f8fafc;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .filter-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 8px 16px;
            border: 2px solid #e5e7eb;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .filter-btn.active {
            background: #10b981;
            color: white;
            border-color: #10b981;
        }

        .edit-form {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 10px;
            padding: 20px;
            margin-top: 15px;
        }

        @media (max-width: 768px) {
            .stats {
                grid-template-columns: 1fr;
            }
            
            .filter-buttons {
                justify-content: center;
            }
            
            .todo-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📝 Todos CRUD Application</h1>
            <p>Complete Todo Management with Supabase</p>
            <div id="connection-status">
                <span id="status-indicator">🔄</span>
                <span id="status-text">Connecting...</span>
            </div>
        </div>
        
        <div class="content">
            <div id="alert-container"></div>
            
            <!-- Statistics Section -->
            <div class="stats" id="stats-section">
                <div class="stat-card">
                    <div class="stat-number" id="total-todos">0</div>
                    <div class="stat-label">Total Todos</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completed-todos">0</div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="pending-todos">0</div>
                    <div class="stat-label">Pending</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="completion-rate">0%</div>
                    <div class="stat-label">Completion Rate</div>
                </div>
            </div>

            <!-- Add New Todo Section -->
            <div class="form-section">
                <h3>➕ Add New Todo</h3>
                <form id="add-todo-form">
                    <div class="form-group">
                        <label for="new-task">Task Description:</label>
                        <textarea id="new-task" rows="3" placeholder="Enter your todo task..." required></textarea>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="new-completed">
                            <label for="new-completed">Mark as completed</label>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Add Todo</button>
                    <button type="button" class="btn btn-secondary" onclick="clearForm()">Clear</button>
                </form>
            </div>

            <!-- Filter Section -->
            <div class="filter-section">
                <h4>🔍 Filter Todos</h4>
                <div class="filter-buttons">
                    <button class="filter-btn active" onclick="filterTodos('all')">All</button>
                    <button class="filter-btn" onclick="filterTodos('pending')">Pending</button>
                    <button class="filter-btn" onclick="filterTodos('completed')">Completed</button>
                </div>

                <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e5e7eb;">
                    <h5>⚡ Bulk Actions</h5>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 10px;">
                        <button class="btn btn-success btn-small" onclick="markAllCompleted()">✅ Complete All</button>
                        <button class="btn btn-danger btn-small" onclick="deleteCompleted()">🗑️ Delete Completed</button>
                        <button class="btn btn-secondary btn-small" onclick="exportTodos()">📥 Export</button>
                    </div>
                </div>
            </div>

            <!-- Todos List Section -->
            <div class="form-section">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3>📋 Your Todos</h3>
                    <button class="btn btn-success" onclick="loadTodos()">🔄 Refresh</button>
                </div>
                
                <div id="todos-container">
                    <div class="loading">Loading todos...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Supabase client
        const supabaseUrl = 'https://ftygxwlkokwgscyflanu.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ0eWd4d2xrb2t3Z3NjeWZsYW51Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTg0NjQ0MjIsImV4cCI6MjA3NDA0MDQyMn0.ChWAPE-f5JFe59emOzN7rB0fl3rh74jlZsgtPwIpo48';
        
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);
        
        let allTodos = [];
        let currentFilter = 'all';
        let editingTodoId = null;

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            testConnection();
            setupFormHandler();
        });

        // Test connection and load todos
        async function testConnection() {
            try {
                updateConnectionStatus('connecting');
                showAlert('Connecting to Supabase...', 'info');
                
                // Test connection by trying to fetch todos
                const { data, error } = await supabase
                    .from('todos')
                    .select('*')
                    .limit(1);

                if (error && error.code === 'PGRST116') {
                    // Table doesn't exist
                    updateConnectionStatus('error');
                    showTableNotExistError();
                    return;
                }

                if (error) {
                    throw error;
                }

                updateConnectionStatus('connected');
                showAlert('✅ Connected to Supabase successfully!', 'success');
                loadTodos();
                
            } catch (error) {
                updateConnectionStatus('error');
                showAlert(`❌ Connection failed: ${error.message}`, 'error');
                console.error('Connection error:', error);
            }
        }

        // Update connection status
        function updateConnectionStatus(status) {
            const indicator = document.getElementById('status-indicator');
            const text = document.getElementById('status-text');

            switch(status) {
                case 'connecting':
                    indicator.textContent = '🔄';
                    text.textContent = 'Connecting...';
                    break;
                case 'connected':
                    indicator.textContent = '✅';
                    text.textContent = 'Connected';
                    break;
                case 'error':
                    indicator.textContent = '❌';
                    text.textContent = 'Connection Error';
                    break;
            }
        }

        // Show table doesn't exist error
        function showTableNotExistError() {
            document.getElementById('todos-container').innerHTML = `
                <div class="empty-state">
                    <h3>📋 Todos Table Not Found</h3>
                    <p>The 'todos' table doesn't exist in your database yet.</p>
                    <p><strong>Please run this SQL in your Supabase SQL Editor:</strong></p>
                    <div style="background: #1f2937; color: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: left; font-family: monospace;">
-- Create todos table<br>
CREATE TABLE todos (<br>
&nbsp;&nbsp;&nbsp;&nbsp;id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,<br>
&nbsp;&nbsp;&nbsp;&nbsp;task TEXT NOT NULL,<br>
&nbsp;&nbsp;&nbsp;&nbsp;completed BOOLEAN DEFAULT FALSE,<br>
&nbsp;&nbsp;&nbsp;&nbsp;created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()<br>
);<br><br>
-- Enable Row Level Security<br>
ALTER TABLE todos ENABLE ROW LEVEL SECURITY;<br><br>
-- Create policy to allow all operations<br>
CREATE POLICY "Allow all operations" ON todos FOR ALL USING (true);
                    </div>
                    <p>After running the SQL, refresh this page.</p>
                    <button class="btn btn-primary" onclick="location.reload()">🔄 Refresh Page</button>
                </div>
            `;
        }

        // Show alert messages
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alert-container');
            const alertClass = type === 'success' ? 'alert-success' :
                             type === 'error' ? 'alert-error' : 'alert-info';

            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // Auto hide after 5 seconds for non-error messages
            if (type !== 'error') {
                setTimeout(() => {
                    alertContainer.innerHTML = '';
                }, 5000);
            }
        }

        // Setup form handler
        function setupFormHandler() {
            document.getElementById('add-todo-form').addEventListener('submit', async function(e) {
                e.preventDefault();
                await addTodo();
            });
        }

        // Load all todos
        async function loadTodos() {
            try {
                showAlert('Loading todos...', 'info');

                const { data, error } = await supabase
                    .from('todos')
                    .select('*')
                    .order('created_at', { ascending: false });

                if (error) throw error;

                allTodos = data || [];
                updateStats();
                displayTodos();
                showAlert(`✅ Loaded ${allTodos.length} todos`, 'success');

            } catch (error) {
                showAlert(`❌ Error loading todos: ${error.message}`, 'error');
                console.error('Load error:', error);
            }
        }

        // Add new todo
        async function addTodo() {
            try {
                const task = document.getElementById('new-task').value.trim();
                const completed = document.getElementById('new-completed').checked;

                if (!task) {
                    showAlert('Please enter a task description', 'error');
                    return;
                }

                showAlert('Adding todo...', 'info');

                const { data, error } = await supabase
                    .from('todos')
                    .insert([{
                        task: task,
                        completed: completed
                    }])
                    .select();

                if (error) throw error;

                showAlert('✅ Todo added successfully!', 'success');
                clearForm();
                loadTodos();

            } catch (error) {
                showAlert(`❌ Error adding todo: ${error.message}`, 'error');
                console.error('Add error:', error);
            }
        }

        // Update todo
        async function updateTodo(id, updates) {
            try {
                showAlert('Updating todo...', 'info');

                const { data, error } = await supabase
                    .from('todos')
                    .update(updates)
                    .eq('id', id)
                    .select();

                if (error) throw error;

                showAlert('✅ Todo updated successfully!', 'success');
                loadTodos();

            } catch (error) {
                showAlert(`❌ Error updating todo: ${error.message}`, 'error');
                console.error('Update error:', error);
            }
        }

        // Delete todo
        async function deleteTodo(id) {
            if (!confirm('Are you sure you want to delete this todo?')) {
                return;
            }

            try {
                showAlert('Deleting todo...', 'info');

                const { error } = await supabase
                    .from('todos')
                    .delete()
                    .eq('id', id);

                if (error) throw error;

                showAlert('✅ Todo deleted successfully!', 'success');
                loadTodos();

            } catch (error) {
                showAlert(`❌ Error deleting todo: ${error.message}`, 'error');
                console.error('Delete error:', error);
            }
        }

        // Toggle todo completion
        async function toggleTodo(id, currentStatus) {
            await updateTodo(id, { completed: !currentStatus });
        }

        // Clear form
        function clearForm() {
            document.getElementById('new-task').value = '';
            document.getElementById('new-completed').checked = false;
            cancelEdit();
        }

        // Display todos
        function displayTodos() {
            const container = document.getElementById('todos-container');

            let filteredTodos = allTodos;

            // Apply filter
            if (currentFilter === 'completed') {
                filteredTodos = allTodos.filter(todo => todo.completed);
            } else if (currentFilter === 'pending') {
                filteredTodos = allTodos.filter(todo => !todo.completed);
            }

            if (filteredTodos.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <h3>📝 No Todos Found</h3>
                        <p>${currentFilter === 'all' ? 'You haven\'t added any todos yet.' :
                             currentFilter === 'completed' ? 'No completed todos.' : 'No pending todos.'}</p>
                        ${currentFilter === 'all' ? '<p>Add your first todo using the form above!</p>' : ''}
                    </div>
                `;
                return;
            }

            const todosHtml = filteredTodos.map(todo => `
                <div class="todo-card ${todo.completed ? 'completed' : ''}" id="todo-${todo.id}">
                    <div class="todo-task">${escapeHtml(todo.task)}</div>
                    <div class="todo-meta">
                        <span class="status-badge ${todo.completed ? 'status-completed' : 'status-pending'}">
                            ${todo.completed ? 'Completed' : 'Pending'}
                        </span>
                        <span style="margin-left: 10px;">
                            Created: ${new Date(todo.created_at).toLocaleDateString()}
                            ${new Date(todo.created_at).toLocaleTimeString()}
                        </span>
                    </div>
                    <div class="todo-actions">
                        <button class="btn btn-${todo.completed ? 'warning' : 'success'} btn-small"
                                onclick="toggleTodo(${todo.id}, ${todo.completed})">
                            ${todo.completed ? '↩️ Mark Pending' : '✅ Mark Complete'}
                        </button>
                        <button class="btn btn-secondary btn-small" onclick="editTodo(${todo.id})">
                            ✏️ Edit
                        </button>
                        <button class="btn btn-danger btn-small" onclick="deleteTodo(${todo.id})">
                            🗑️ Delete
                        </button>
                    </div>
                    <div id="edit-form-${todo.id}"></div>
                </div>
            `).join('');

            container.innerHTML = `<div class="todos-grid">${todosHtml}</div>`;
        }

        // Update statistics
        function updateStats() {
            const total = allTodos.length;
            const completed = allTodos.filter(todo => todo.completed).length;
            const pending = total - completed;
            const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0;

            document.getElementById('total-todos').textContent = total;
            document.getElementById('completed-todos').textContent = completed;
            document.getElementById('pending-todos').textContent = pending;
            document.getElementById('completion-rate').textContent = `${completionRate}%`;
        }

        // Filter todos
        function filterTodos(filter) {
            currentFilter = filter;

            // Update filter buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            displayTodos();
        }

        // Edit todo
        function editTodo(id) {
            const todo = allTodos.find(t => t.id === id);
            if (!todo) return;

            // Cancel any existing edit
            cancelEdit();

            editingTodoId = id;

            const editFormHtml = `
                <div class="edit-form">
                    <h4>✏️ Edit Todo</h4>
                    <div class="form-group">
                        <label>Task Description:</label>
                        <textarea id="edit-task-${id}" rows="3">${escapeHtml(todo.task)}</textarea>
                    </div>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="edit-completed-${id}" ${todo.completed ? 'checked' : ''}>
                            <label for="edit-completed-${id}">Completed</label>
                        </div>
                    </div>
                    <button class="btn btn-primary btn-small" onclick="saveEdit(${id})">💾 Save</button>
                    <button class="btn btn-secondary btn-small" onclick="cancelEdit()">❌ Cancel</button>
                </div>
            `;

            document.getElementById(`edit-form-${id}`).innerHTML = editFormHtml;
        }

        // Save edit
        async function saveEdit(id) {
            const task = document.getElementById(`edit-task-${id}`).value.trim();
            const completed = document.getElementById(`edit-completed-${id}`).checked;

            if (!task) {
                showAlert('Please enter a task description', 'error');
                return;
            }

            await updateTodo(id, { task, completed });
            cancelEdit();
        }

        // Cancel edit
        function cancelEdit() {
            if (editingTodoId) {
                document.getElementById(`edit-form-${editingTodoId}`).innerHTML = '';
                editingTodoId = null;
            }
        }

        // Escape HTML to prevent XSS
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Bulk operations
        async function markAllCompleted() {
            if (!confirm('Mark all todos as completed?')) return;

            try {
                showAlert('Updating all todos...', 'info');

                const { error } = await supabase
                    .from('todos')
                    .update({ completed: true })
                    .eq('completed', false);

                if (error) throw error;

                showAlert('✅ All todos marked as completed!', 'success');
                loadTodos();

            } catch (error) {
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function deleteCompleted() {
            if (!confirm('Delete all completed todos? This cannot be undone.')) return;

            try {
                showAlert('Deleting completed todos...', 'info');

                const { error } = await supabase
                    .from('todos')
                    .delete()
                    .eq('completed', true);

                if (error) throw error;

                showAlert('✅ Completed todos deleted!', 'success');
                loadTodos();

            } catch (error) {
                showAlert(`❌ Error: ${error.message}`, 'error');
            }
        }

        // Export todos
        function exportTodos() {
            if (allTodos.length === 0) {
                showAlert('No todos to export', 'error');
                return;
            }

            const exportData = allTodos.map(todo => ({
                id: todo.id,
                task: todo.task,
                completed: todo.completed,
                created_at: todo.created_at
            }));

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `todos_export_${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showAlert('✅ Todos exported successfully!', 'success');
        }

        // Auto-refresh every 30 seconds to sync with other users
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                loadTodos();
            }
        }, 30000);

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + Enter to add todo
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                document.getElementById('add-todo-form').dispatchEvent(new Event('submit'));
            }

            // Escape to cancel edit
            if (e.key === 'Escape') {
                cancelEdit();
            }

            // Ctrl/Cmd + R to refresh (prevent default and use our function)
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                loadTodos();
            }
        });

        // Focus on task input when page loads
        window.addEventListener('load', function() {
            setTimeout(() => {
                document.getElementById('new-task').focus();
            }, 1000);
        });
    </script>
</body>
</html>
