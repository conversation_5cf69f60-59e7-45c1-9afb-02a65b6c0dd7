<?php
// config.php - Database configuration
$host = '************';
$port = '5432';
$dbname = 'postgres';
$username = 'postgres';
$password = 'YOUR_PASSWORD_HERE'; // Replace with your actual password

try {
    $pdo = new PDO("pgsql:host=$host;port=$port;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}
?>
