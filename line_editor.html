<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Line Range Editor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        
        .file-input {
            margin: 10px 0;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .range-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f8f9fa;
        }
        
        .range-item {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .range-item:last-child {
            margin-bottom: 0;
        }
        
        .range-inputs {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn-remove {
            background-color: #6c757d;
            color: white;
            padding: 5px 10px;
            font-size: 12px;
        }
        
        .btn-remove:hover {
            background-color: #545b62;
        }
        
        .btn-add {
            background-color: #007bff;
            color: white;
        }
        
        .btn-add:hover {
            background-color: #0056b3;
        }
        
        .range-summary {
            margin-top: 10px;
            padding: 10px;
            background-color: #e7f3ff;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .controls label {
            font-weight: bold;
            color: #555;
        }
        
        .controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 80px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn-delete {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background-color: #c82333;
        }
        
        .btn-download {
            background-color: #28a745;
            color: white;
        }
        
        .btn-download:hover {
            background-color: #218838;
        }
        
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .editor-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        
        .line-numbers {
            background-color: #f8f9fa;
            padding: 15px 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #666;
            min-width: 50px;
            text-align: right;
            border-right: 2px solid #dee2e6;
        }
        
        .file-content {
            flex: 1;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
            min-height: 400px;
            border: 1px solid #dee2e6;
        }
        
        .highlight-range {
            background-color: #ffebee !important;
            border-left: 3px solid #f44336;
            padding-left: 12px;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .file-info {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 Line Range Editor</h1>
        
        <div class="upload-section">
            <h3>Upload File</h3>
            <input type="file" id="fileInput" class="file-input" accept=".txt,.js,.html,.css,.php,.py,.java,.cpp,.c,.h,.json">
            <p>Select a text file to edit</p>
        </div>
        
        <div id="fileInfo" class="file-info" style="display: none;">
            <strong>File:</strong> <span id="fileName"></span> | 
            <strong>Size:</strong> <span id="fileSize"></span> | 
            <strong>Lines:</strong> <span id="lineCount"></span>
        </div>
        
        <div class="range-container">
            <h3>📍 Line Ranges to Delete</h3>
            <div id="rangeList">
                <!-- Range items will be added here -->
            </div>
            <button class="btn btn-add" onclick="addNewRange()">➕ Add Range</button>
            <div id="rangeSummary" class="range-summary" style="display: none;"></div>
        </div>
        
        <div class="controls">
            <button class="btn btn-delete" id="deleteBtn" onclick="deleteLines()" disabled>
                🗑️ Delete Selected Ranges
            </button>
            
            <button class="btn btn-download" id="downloadBtn" onclick="downloadFile()" disabled>
                💾 Download Modified File
            </button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="editor-container" id="editorContainer" style="display: none;">
            <div class="line-numbers" id="lineNumbers"></div>
            <div class="file-content" id="fileContent"></div>
        </div>
    </div>

    <script>
        let fileContent = '';
        let fileName = '';
        let lines = [];
        let ranges = [];
        let rangeCounter = 0;

        // File input handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                fileName = file.name;
                const reader = new FileReader();
                reader.onload = function(e) {
                    fileContent = e.target.result;
                    lines = fileContent.split('\n');
                    displayFile();
                    updateFileInfo(file);
                    enableControls();
                };
                reader.readAsText(file);
            }
        });

        function updateFileInfo(file) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('lineCount').textContent = lines.length;
            document.getElementById('fileInfo').style.display = 'block';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function displayFile() {
            const lineNumbersDiv = document.getElementById('lineNumbers');
            const fileContentDiv = document.getElementById('fileContent');
            
            // Generate line numbers
            let lineNumbersHtml = '';
            for (let i = 1; i <= lines.length; i++) {
                lineNumbersHtml += i + '\n';
            }
            lineNumbersDiv.textContent = lineNumbersHtml;
            
            // Display file content
            fileContentDiv.textContent = fileContent;
            
            // Show editor container
            document.getElementById('editorContainer').style.display = 'flex';
            
            // Update max values for input fields
            document.getElementById('fromLine').max = lines.length;
            document.getElementById('toLine').max = lines.length;
            document.getElementById('toLine').value = lines.length;
        }

        function enableControls() {
            document.getElementById('deleteBtn').disabled = false;
            document.getElementById('downloadBtn').disabled = false;
            // Add initial range
            addNewRange();
        }

        function addNewRange() {
            rangeCounter++;
            const rangeId = `range_${rangeCounter}`;
            
            const rangeItem = document.createElement('div');
            rangeItem.className = 'range-item';
            rangeItem.id = rangeId;
            
            rangeItem.innerHTML = `
                <div class="range-inputs">
                    <label>From:</label>
                    <input type="number" id="${rangeId}_from" min="1" max="${lines.length}" placeholder="1" onchange="updateRangeSummary()">
                    <label>To:</label>
                    <input type="number" id="${rangeId}_to" min="1" max="${lines.length}" placeholder="10" onchange="updateRangeSummary()">
                    <button class="btn btn-remove" onclick="removeRange('${rangeId}')">
                        ✖️ Remove
                    </button>
                </div>
            `;
            
            document.getElementById('rangeList').appendChild(rangeItem);
            updateRangeSummary();
        }
        
        function removeRange(rangeId) {
            const rangeElement = document.getElementById(rangeId);
            if (rangeElement) {
                rangeElement.remove();
                updateRangeSummary();
            }
        }
        
        function updateRangeSummary() {
            const rangeItems = document.querySelectorAll('.range-item');
            const summary = document.getElementById('rangeSummary');
            
            if (rangeItems.length === 0) {
                summary.style.display = 'none';
                return;
            }
            
            let validRanges = [];
            let totalLines = 0;
            
            rangeItems.forEach(item => {
                const rangeId = item.id;
                const fromInput = document.getElementById(`${rangeId}_from`);
                const toInput = document.getElementById(`${rangeId}_to`);
                
                if (fromInput && toInput) {
                    const from = parseInt(fromInput.value);
                    const to = parseInt(toInput.value);
                    
                    if (!isNaN(from) && !isNaN(to) && from >= 1 && to >= from && to <= lines.length) {
                        validRanges.push({ from, to });
                        totalLines += (to - from + 1);
                    }
                }
            });
            
            if (validRanges.length > 0) {
                summary.innerHTML = `
                    <strong>Summary:</strong> ${validRanges.length} range(s) selected, ${totalLines} line(s) will be deleted<br>
                    <strong>Ranges:</strong> ${validRanges.map(r => `${r.from}-${r.to}`).join(', ')}
                `;
                summary.style.display = 'block';
            } else {
                summary.style.display = 'none';
            }
        }

        function deleteLines() {
            const rangeItems = document.querySelectorAll('.range-item');
            
            if (rangeItems.length === 0) {
                showStatus('Please add at least one line range to delete', 'error');
                return;
            }
            
            // Collect all valid ranges
            let validRanges = [];
            
            rangeItems.forEach(item => {
                const rangeId = item.id;
                const fromInput = document.getElementById(`${rangeId}_from`);
                const toInput = document.getElementById(`${rangeId}_to`);
                
                if (fromInput && toInput) {
                    const from = parseInt(fromInput.value);
                    const to = parseInt(toInput.value);
                    
                    // Validation for each range
                    if (isNaN(from) || isNaN(to)) {
                        showStatus('Please enter valid line numbers for all ranges', 'error');
                        return;
                    }
                    
                    if (from < 1 || to < 1) {
                        showStatus('Line numbers must be greater than 0', 'error');
                        return;
                    }
                    
                    if (from > lines.length || to > lines.length) {
                        showStatus(`Line numbers cannot exceed total lines (${lines.length})`, 'error');
                        return;
                    }
                    
                    if (from > to) {
                        showStatus('From line must be less than or equal to To line in all ranges', 'error');
                        return;
                    }
                    
                    validRanges.push({ from, to });
                }
            });
            
            if (validRanges.length === 0) {
                showStatus('No valid ranges found', 'error');
                return;
            }
            
            // Sort ranges by starting line (descending) to delete from bottom to top
            validRanges.sort((a, b) => b.from - a.from);
            
            // Check for overlapping ranges
            for (let i = 0; i < validRanges.length - 1; i++) {
                const current = validRanges[i];
                const next = validRanges[i + 1];
                if (current.from <= next.to) {
                    showStatus('Ranges cannot overlap. Please check your line ranges.', 'error');
                    return;
                }
            }
            
            // Calculate total lines to be deleted
            const totalLinesToDelete = validRanges.reduce((sum, range) => sum + (range.to - range.from + 1), 0);
            
            // Confirm deletion
            const rangeText = validRanges.map(r => `${r.from}-${r.to}`).join(', ');
            const confirmMsg = `Are you sure you want to delete the following line ranges?\n\nRanges: ${rangeText}\nTotal lines to delete: ${totalLinesToDelete}\n\nThis action cannot be undone.`;
            
            if (!confirm(confirmMsg)) {
                return;
            }
            
            // Delete the lines (from bottom to top to maintain line numbers)
            let totalDeleted = 0;
            validRanges.forEach(range => {
                const deleteCount = range.to - range.from + 1;
                lines.splice(range.from - 1, deleteCount);
                totalDeleted += deleteCount;
            });
            
            // Update file content
            fileContent = lines.join('\n');
            
            // Refresh display
            displayFile();
            
            // Clear all ranges and add a new empty one
            document.getElementById('rangeList').innerHTML = '';
            addNewRange();
            
            // Show success message
            showStatus(`Successfully deleted ${totalDeleted} line(s) from ${validRanges.length} range(s).`, 'success');
        }

        function downloadFile() {
            if (!fileContent) {
                showStatus('No file content to download', 'error');
                return;
            }
            
            const blob = new Blob([fileContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'modified_' + fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            showStatus('File downloaded successfully!', 'success');
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
