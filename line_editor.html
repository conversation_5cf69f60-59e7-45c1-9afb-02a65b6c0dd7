<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Line Range Editor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
        }
        
        .file-input {
            margin: 10px 0;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .controls label {
            font-weight: bold;
            color: #555;
        }
        
        .controls input {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 80px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn-delete {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-delete:hover {
            background-color: #c82333;
        }
        
        .btn-download {
            background-color: #28a745;
            color: white;
        }
        
        .btn-download:hover {
            background-color: #218838;
        }
        
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .editor-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        
        .line-numbers {
            background-color: #f8f9fa;
            padding: 15px 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #666;
            min-width: 50px;
            text-align: right;
            border-right: 2px solid #dee2e6;
        }
        
        .file-content {
            flex: 1;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            white-space: pre-wrap;
            overflow-x: auto;
            min-height: 400px;
            border: 1px solid #dee2e6;
        }
        
        .highlight-range {
            background-color: #ffebee !important;
            border-left: 3px solid #f44336;
            padding-left: 12px;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .file-info {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📝 Line Range Editor</h1>
        
        <div class="upload-section">
            <h3>Upload File</h3>
            <input type="file" id="fileInput" class="file-input" accept=".txt,.js,.html,.css,.php,.py,.java,.cpp,.c,.h">
            <p>Select a text file to edit</p>
        </div>
        
        <div id="fileInfo" class="file-info" style="display: none;">
            <strong>File:</strong> <span id="fileName"></span> | 
            <strong>Size:</strong> <span id="fileSize"></span> | 
            <strong>Lines:</strong> <span id="lineCount"></span>
        </div>
        
        <div class="controls">
            <label for="fromLine">From Line:</label>
            <input type="number" id="fromLine" min="1" placeholder="1">
            
            <label for="toLine">To Line:</label>
            <input type="number" id="toLine" min="1" placeholder="10">
            
            <button class="btn btn-delete" id="deleteBtn" onclick="deleteLines()" disabled>
                🗑️ Delete Lines
            </button>
            
            <button class="btn btn-download" id="downloadBtn" onclick="downloadFile()" disabled>
                💾 Download Modified File
            </button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="editor-container" id="editorContainer" style="display: none;">
            <div class="line-numbers" id="lineNumbers"></div>
            <div class="file-content" id="fileContent"></div>
        </div>
    </div>

    <script>
        let fileContent = '';
        let fileName = '';
        let lines = [];

        // File input handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                fileName = file.name;
                const reader = new FileReader();
                reader.onload = function(e) {
                    fileContent = e.target.result;
                    lines = fileContent.split('\n');
                    displayFile();
                    updateFileInfo(file);
                    enableControls();
                };
                reader.readAsText(file);
            }
        });

        function updateFileInfo(file) {
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);
            document.getElementById('lineCount').textContent = lines.length;
            document.getElementById('fileInfo').style.display = 'block';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function displayFile() {
            const lineNumbersDiv = document.getElementById('lineNumbers');
            const fileContentDiv = document.getElementById('fileContent');
            
            // Generate line numbers
            let lineNumbersHtml = '';
            for (let i = 1; i <= lines.length; i++) {
                lineNumbersHtml += i + '\n';
            }
            lineNumbersDiv.textContent = lineNumbersHtml;
            
            // Display file content
            fileContentDiv.textContent = fileContent;
            
            // Show editor container
            document.getElementById('editorContainer').style.display = 'flex';
            
            // Update max values for input fields
            document.getElementById('fromLine').max = lines.length;
            document.getElementById('toLine').max = lines.length;
            document.getElementById('toLine').value = lines.length;
        }

        function enableControls() {
            document.getElementById('deleteBtn').disabled = false;
            document.getElementById('downloadBtn').disabled = false;
        }

        function highlightRange() {
            const fromLine = parseInt(document.getElementById('fromLine').value);
            const toLine = parseInt(document.getElementById('toLine').value);
            
            if (isNaN(fromLine) || isNaN(toLine)) return;
            
            const fileContentDiv = document.getElementById('fileContent');
            const lineNumbersDiv = document.getElementById('lineNumbers');
            
            // Remove previous highlights
            fileContentDiv.classList.remove('highlight-range');
            lineNumbersDiv.classList.remove('highlight-range');
            
            // Add highlight if valid range
            if (fromLine >= 1 && toLine >= fromLine && toLine <= lines.length) {
                // This is a simple highlight - in a more complex implementation,
                // you could highlight specific line ranges
                showStatus(`Will delete lines ${fromLine} to ${toLine}`, 'info');
            }
        }

        // Add event listeners for range highlighting
        document.getElementById('fromLine').addEventListener('input', highlightRange);
        document.getElementById('toLine').addEventListener('input', highlightRange);

        function deleteLines() {
            const fromLine = parseInt(document.getElementById('fromLine').value);
            const toLine = parseInt(document.getElementById('toLine').value);
            
            // Validation
            if (isNaN(fromLine) || isNaN(toLine)) {
                showStatus('Please enter valid line numbers', 'error');
                return;
            }
            
            if (fromLine < 1 || toLine < 1) {
                showStatus('Line numbers must be greater than 0', 'error');
                return;
            }
            
            if (fromLine > lines.length || toLine > lines.length) {
                showStatus('Line numbers cannot exceed total lines (' + lines.length + ')', 'error');
                return;
            }
            
            if (fromLine > toLine) {
                showStatus('From line must be less than or equal to To line', 'error');
                return;
            }
            
            // Confirm deletion
            const confirmMsg = `Are you sure you want to delete lines ${fromLine} to ${toLine}?\nThis will remove ${toLine - fromLine + 1} line(s).`;
            if (!confirm(confirmMsg)) {
                return;
            }
            
            // Delete the lines (convert to 0-based index)
            const deletedLines = toLine - fromLine + 1;
            lines.splice(fromLine - 1, deletedLines);
            
            // Update file content
            fileContent = lines.join('\n');
            
            // Refresh display
            displayFile();
            
            // Clear input fields
            document.getElementById('fromLine').value = '';
            document.getElementById('toLine').value = '';
            
            // Show success message
            showStatus(`Successfully deleted ${deletedLines} line(s). Lines ${fromLine}-${toLine} have been removed.`, 'success');
        }

        function downloadFile() {
            if (!fileContent) {
                showStatus('No file content to download', 'error');
                return;
            }
            
            const blob = new Blob([fileContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'modified_' + fileName;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            
            showStatus('File downloaded successfully!', 'success');
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + type;
            statusDiv.style.display = 'block';
            
            // Auto-hide after 5 seconds
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html>
