<?php
// config.php - Database configuration
$host = '************';
$port = '5432';
$dbname = 'postgres';
$username = 'postgres';
$password = '<PERSON>koii@2025'; // Replace with your actual password

$pdo = null;
$connection_error = null;

try {
    $pdo = new PDO("pgsql:host=$host;port=$port;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    $connection_error = "Connection failed: " . $e->getMessage();
    // Only die if this is not an API call
    if (!isset($_SERVER['HTTP_ACCEPT']) || strpos($_SERVER['HTTP_ACCEPT'], 'application/json') === false) {
        die($connection_error);
    }
}
?>
