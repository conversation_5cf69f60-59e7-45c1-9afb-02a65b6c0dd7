<?php
// config.php - Database configuration
$host = '************';
$port = '5432';
$dbname = 'postgres';
$username = 'postgres';
$password = '<PERSON>ko<PERSON>@2025'; // Replace with your actual password

$pdo = null;
$connection_error = null;

try {
    // Try connection with SSL first
    $pdo = new PDO("pgsql:host=$host;port=$port;dbname=$dbname;sslmode=require", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    // If SSL fails, try without SSL
    try {
        $pdo = new PDO("pgsql:host=$host;port=$port;dbname=$dbname", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    } catch(PDOException $e2) {
        $connection_error = "Connection failed: " . $e2->getMessage();
        // Only die if this is not an API call
        if (!isset($_SERVER['HTTP_ACCEPT']) || strpos($_SERVER['HTTP_ACCEPT'], 'application/json') === false) {
            die($connection_error);
        }
    }
}
?>
