<?php
// check_php.php - Check PHP configuration for PostgreSQL
echo "<h2>PHP Configuration Check</h2>";

echo "<h3>PHP Version</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";

echo "<h3>PDO Extensions</h3>";
$pdo_drivers = PDO::getAvailableDrivers();
echo "<p>Available PDO drivers: " . implode(', ', $pdo_drivers) . "</p>";

if (in_array('pgsql', $pdo_drivers)) {
    echo "<p style='color: green;'>✅ PostgreSQL PDO driver is available</p>";
} else {
    echo "<p style='color: red;'>❌ PostgreSQL PDO driver is NOT available</p>";
    echo "<p><strong>Solution:</strong> You need to enable the PostgreSQL extension in PHP.</p>";
    echo "<p>For XAMPP:</p>";
    echo "<ol>";
    echo "<li>Open php.ini file (usually in C:\\xampp\\php\\php.ini)</li>";
    echo "<li>Find the line: ;extension=pdo_pgsql</li>";
    echo "<li>Remove the semicolon to uncomment it: extension=pdo_pgsql</li>";
    echo "<li>Also uncomment: extension=pgsql</li>";
    echo "<li>Restart Apache</li>";
    echo "</ol>";
}

echo "<h3>All Loaded Extensions</h3>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<p>Loaded extensions:</p>";
echo "<ul>";
foreach ($extensions as $ext) {
    $color = (strpos($ext, 'pdo') !== false || strpos($ext, 'pgsql') !== false) ? 'color: green; font-weight: bold;' : '';
    echo "<li style='$color'>$ext</li>";
}
echo "</ul>";

echo "<h3>PostgreSQL Specific Extensions</h3>";
$pgsql_extensions = ['pgsql', 'pdo_pgsql'];
foreach ($pgsql_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<p style='color: green;'>✅ $ext is loaded</p>";
    } else {
        echo "<p style='color: red;'>❌ $ext is NOT loaded</p>";
    }
}
?>
