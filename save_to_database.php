<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log debug information
$debug_log = [];
$debug_log[] = "Script started at: " . date('Y-m-d H:i:s');
$debug_log[] = "Request method: " . $_SERVER['REQUEST_METHOD'];
$debug_log[] = "Current working directory: " . getcwd();
$debug_log[] = "Script file: " . __FILE__;

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'error' => 'Method not allowed',
        'debug' => $debug_log
    ]);
    exit;
}

try {
    // Get JSON input
    $input = file_get_contents('php://input');
    $debug_log[] = "Raw input received: " . $input;
    $debug_log[] = "Input length: " . strlen($input);

    $data = json_decode($input, true);
    $debug_log[] = "JSON decode result: " . ($data ? 'SUCCESS' : 'FAILED');
    $debug_log[] = "JSON error: " . json_last_error_msg();

    if (!$data) {
        throw new Exception('Invalid JSON data - ' . json_last_error_msg());
    }

    $debug_log[] = "Decoded data keys: " . implode(', ', array_keys($data));
    
    // Validate required fields
    $required_fields = ['originalFileName', 'uploadedFileName', 'bucket', 'size', 'uploadDate', 'downloadUrl', 'publicUrl'];
    foreach ($required_fields as $field) {
        if (!isset($data[$field])) {
            throw new Exception("Missing required field: $field");
        }
    }
    
    // CSV file path
    $csv_file = 'db_gcs_links.csv';
    $csv_file_full_path = realpath($csv_file);

    $debug_log[] = "CSV file path: " . $csv_file;
    $debug_log[] = "CSV file full path: " . ($csv_file_full_path ?: 'NOT FOUND');
    $debug_log[] = "File exists: " . (file_exists($csv_file) ? 'YES' : 'NO');
    $debug_log[] = "File readable: " . (is_readable($csv_file) ? 'YES' : 'NO');
    $debug_log[] = "File writable: " . (is_writable($csv_file) ? 'YES' : 'NO');
    $debug_log[] = "Directory writable: " . (is_writable(dirname($csv_file)) ? 'YES' : 'NO');

    // Check if file exists, if not create with headers
    if (!file_exists($csv_file)) {
        $debug_log[] = "Creating new CSV file with headers";
        $headers = [
            'ID',
            'Original File Name',
            'Uploaded File Name',
            'Bucket',
            'File Size (Bytes)',
            'File Size (Formatted)',
            'Upload Date',
            'Download URL',
            'Public URL',
            'Status'
        ];
        
        $fp = fopen($csv_file, 'w');
        if (!$fp) {
            $debug_log[] = "FAILED to create CSV file";
            throw new Exception('Cannot create CSV file - ' . error_get_last()['message']);
        }
        $debug_log[] = "CSV file created successfully";

        $header_result = fputcsv($fp, $headers);
        $debug_log[] = "Header write result: " . ($header_result ? 'SUCCESS' : 'FAILED');
        fclose($fp);
        $debug_log[] = "CSV file closed after header creation";
    } else {
        $debug_log[] = "CSV file already exists, skipping header creation";
    }
    
    // Format file size
    function formatFileSize($bytes) {
        if ($bytes == 0) return '0 Bytes';
        $k = 1024;
        $sizes = ['Bytes', 'KB', 'MB', 'GB'];
        $i = floor(log($bytes) / log($k));
        return round(($bytes / pow($k, $i)), 2) . ' ' . $sizes[$i];
    }
    
    // Prepare CSV row
    $csv_row = [
        time(), // ID (timestamp)
        $data['originalFileName'],
        $data['uploadedFileName'],
        $data['bucket'],
        $data['size'],
        formatFileSize($data['size']),
        $data['uploadDate'],
        $data['downloadUrl'],
        $data['publicUrl'],
        'Active'
    ];
    
    $debug_log[] = "Attempting to append to CSV file";
    $debug_log[] = "CSV row data: " . json_encode($csv_row);

    // Append to CSV file
    $fp = fopen($csv_file, 'a');
    if (!$fp) {
        $debug_log[] = "FAILED to open CSV file for appending";
        throw new Exception('Cannot open CSV file for writing - ' . error_get_last()['message']);
    }
    $debug_log[] = "CSV file opened for appending successfully";

    $write_result = fputcsv($fp, $csv_row);
    $debug_log[] = "CSV write result: " . ($write_result ? 'SUCCESS (' . $write_result . ' bytes)' : 'FAILED');

    if ($write_result === false) {
        fclose($fp);
        $debug_log[] = "CSV write failed, file closed";
        throw new Exception('Failed to write to CSV file');
    }

    fclose($fp);
    $debug_log[] = "CSV file closed after successful write";

    // Verify the write by reading the file
    $file_contents = file_get_contents($csv_file);
    $debug_log[] = "File size after write: " . strlen($file_contents) . " bytes";
    $debug_log[] = "Last 100 characters: " . substr($file_contents, -100);

    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Record saved to database',
        'record' => $csv_row,
        'debug' => $debug_log
    ]);
    
} catch (Exception $e) {
    $debug_log[] = "EXCEPTION: " . $e->getMessage();
    $debug_log[] = "Exception file: " . $e->getFile();
    $debug_log[] = "Exception line: " . $e->getLine();

    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'debug' => $debug_log
    ]);
}
?>
