<?php
// api.php - CRUD operations API
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch($method) {
        case 'GET':
            if(isset($_GET['id'])) {
                // Get single user
                $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$_GET['id']]);
                $user = $stmt->fetch();
                echo json_encode($user ?: ['error' => 'User not found']);
            } else {
                // Get all users
                $stmt = $pdo->query("SELECT * FROM users ORDER BY id DESC");
                $users = $stmt->fetchAll();
                echo json_encode($users);
            }
            break;

        case 'POST':
            // Create new user
            if(!isset($input['name']) || !isset($input['email'])) {
                echo json_encode(['error' => 'Name and email are required']);
                break;
            }
            
            $stmt = $pdo->prepare("INSERT INTO users (name, email, phone) VALUES (?, ?, ?) RETURNING id");
            $stmt->execute([$input['name'], $input['email'], $input['phone'] ?? null]);
            $id = $stmt->fetchColumn();
            
            echo json_encode(['success' => true, 'id' => $id, 'message' => 'User created successfully']);
            break;

        case 'PUT':
            // Update user
            if(!isset($input['id'])) {
                echo json_encode(['error' => 'User ID is required']);
                break;
            }
            
            $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ?, phone = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $result = $stmt->execute([$input['name'], $input['email'], $input['phone'], $input['id']]);
            
            if($stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'User updated successfully']);
            } else {
                echo json_encode(['error' => 'User not found or no changes made']);
            }
            break;

        case 'DELETE':
            // Delete user
            if(!isset($_GET['id'])) {
                echo json_encode(['error' => 'User ID is required']);
                break;
            }
            
            $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
            $result = $stmt->execute([$_GET['id']]);
            
            if($stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
            } else {
                echo json_encode(['error' => 'User not found']);
            }
            break;

        default:
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
} catch(PDOException $e) {
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>
