<?php
// supabase_cli_checker.php - Check Supabase organization and databases
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supabase CLI Checker</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .terminal {
            background: #000;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #333;
            max-width: 1000px;
            margin: 0 auto;
        }
        .prompt {
            color: #00ff00;
        }
        .error {
            color: #ff4444;
        }
        .success {
            color: #44ff44;
        }
        .info {
            color: #4444ff;
        }
        .warning {
            color: #ffff44;
        }
        pre {
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .section {
            border-top: 1px solid #333;
            margin-top: 20px;
            padding-top: 20px;
        }
        .command {
            background: #222;
            padding: 10px;
            border-left: 3px solid #00ff00;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="terminal">
        <pre class="prompt">
╔══════════════════════════════════════════════════════════════════════════════╗
║                           SUPABASE DATABASE CHECKER                         ║
║                          Check your Supabase organization                   ║
╚══════════════════════════════════════════════════════════════════════════════╝
        </pre>

        <?php
        echo "<div class='section'>\n";
        echo "<span class='info'>🔍 CHECKING SUPABASE CLI INSTALLATION...</span>\n\n";

        // Check if Supabase CLI is installed
        $supabase_check = shell_exec('supabase --version 2>&1');
        
        if (strpos($supabase_check, 'supabase') !== false) {
            echo "<span class='success'>✅ Supabase CLI is installed</span>\n";
            echo "<pre>Version: " . trim($supabase_check) . "</pre>\n";
        } else {
            echo "<span class='error'>❌ Supabase CLI is not installed</span>\n";
            echo "<div class='command'>\n";
            echo "To install Supabase CLI:\n\n";
            echo "# Using npm:\n";
            echo "npm install -g supabase\n\n";
            echo "# Using Homebrew (macOS):\n";
            echo "brew install supabase/tap/supabase\n\n";
            echo "# Using Scoop (Windows):\n";
            echo "scoop bucket add supabase https://github.com/supabase/scoop-bucket.git\n";
            echo "scoop install supabase\n";
            echo "</div>\n";
        }

        echo "</div>\n";

        // Check login status
        echo "<div class='section'>\n";
        echo "<span class='info'>🔐 CHECKING LOGIN STATUS...</span>\n\n";

        $login_check = shell_exec('supabase projects list 2>&1');
        
        if (strpos($login_check, 'Error') !== false || strpos($login_check, 'not logged in') !== false) {
            echo "<span class='warning'>⚠️ Not logged in to Supabase</span>\n";
            echo "<div class='command'>\n";
            echo "To login:\n";
            echo "supabase login\n";
            echo "</div>\n";
        } else {
            echo "<span class='success'>✅ Logged in to Supabase</span>\n";
            
            // List projects
            echo "\n<span class='info'>📋 YOUR SUPABASE PROJECTS:</span>\n";
            echo "<pre>" . htmlspecialchars($login_check) . "</pre>\n";
        }

        echo "</div>\n";

        // Manual connection instructions
        echo "<div class='section'>\n";
        echo "<span class='info'>🔗 MANUAL CONNECTION METHODS:</span>\n\n";
        
        echo "<span class='warning'>Method 1: Using Supabase Dashboard</span>\n";
        echo "<div class='command'>\n";
        echo "1. Go to https://supabase.com/dashboard\n";
        echo "2. Select your project\n";
        echo "3. Go to Settings → Database\n";
        echo "4. Copy connection strings\n";
        echo "</div>\n";

        echo "<span class='warning'>Method 2: Using API</span>\n";
        echo "<div class='command'>\n";
        echo "1. Get your Project URL: https://your-project.supabase.co\n";
        echo "2. Get your API Key from Settings → API\n";
        echo "3. Use REST API to query tables\n";
        echo "</div>\n";

        echo "<span class='warning'>Method 3: Direct Database Connection</span>\n";
        echo "<div class='command'>\n";
        echo "Connection String Format:\n";
        echo "postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres\n\n";
        echo "Pooled Connection (Session Mode):\n";
        echo "postgres://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-[REGION].pooler.supabase.com:5432/postgres\n\n";
        echo "Pooled Connection (Transaction Mode):\n";
        echo "postgres://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-[REGION].pooler.supabase.com:6543/postgres\n";
        echo "</div>\n";

        echo "</div>\n";

        // Common commands
        echo "<div class='section'>\n";
        echo "<span class='info'>⚡ USEFUL SUPABASE CLI COMMANDS:</span>\n\n";
        
        $commands = [
            "supabase login" => "Login to your Supabase account",
            "supabase projects list" => "List all your projects",
            "supabase projects create [name]" => "Create a new project",
            "supabase link --project-ref [ref]" => "Link local project to remote",
            "supabase start" => "Start local development environment",
            "supabase status" => "Check status of local services",
            "supabase db pull" => "Pull remote database schema",
            "supabase db push" => "Push local schema to remote",
            "supabase db reset" => "Reset local database",
            "supabase gen types typescript" => "Generate TypeScript types"
        ];

        foreach ($commands as $command => $description) {
            echo "<span class='success'>$command</span>\n";
            echo "  → $description\n\n";
        }

        echo "</div>\n";

        // Environment check
        echo "<div class='section'>\n";
        echo "<span class='info'>🔧 ENVIRONMENT CHECK:</span>\n\n";

        // Check Node.js
        $node_check = shell_exec('node --version 2>&1');
        if (strpos($node_check, 'v') === 0) {
            echo "<span class='success'>✅ Node.js: " . trim($node_check) . "</span>\n";
        } else {
            echo "<span class='error'>❌ Node.js not found</span>\n";
        }

        // Check npm
        $npm_check = shell_exec('npm --version 2>&1');
        if (is_numeric(trim($npm_check))) {
            echo "<span class='success'>✅ npm: " . trim($npm_check) . "</span>\n";
        } else {
            echo "<span class='error'>❌ npm not found</span>\n";
        }

        // Check Git
        $git_check = shell_exec('git --version 2>&1');
        if (strpos($git_check, 'git version') !== false) {
            echo "<span class='success'>✅ Git: " . trim($git_check) . "</span>\n";
        } else {
            echo "<span class='error'>❌ Git not found</span>\n";
        }

        echo "</div>\n";

        // Next steps
        echo "<div class='section'>\n";
        echo "<span class='info'>🚀 NEXT STEPS:</span>\n\n";
        echo "1. <span class='warning'>Install Supabase CLI</span> (if not already installed)\n";
        echo "2. <span class='warning'>Login to your account:</span> supabase login\n";
        echo "3. <span class='warning'>List your projects:</span> supabase projects list\n";
        echo "4. <span class='warning'>Create or link a project</span>\n";
        echo "5. <span class='warning'>Start local development:</span> supabase start\n";
        echo "6. <span class='warning'>Access local dashboard:</span> http://localhost:54323\n";
        echo "</div>\n";
        ?>

        <pre class="prompt">
╔══════════════════════════════════════════════════════════════════════════════╗
║                              END OF CHECK                                   ║
╚══════════════════════════════════════════════════════════════════════════════╝
        </pre>
    </div>
</body>
</html>
