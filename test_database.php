<?php
// Test script to debug CSV database writing
echo "<h2>🔍 Database Debug Test</h2>";

// Test 1: Check file permissions and paths
echo "<h3>📁 File System Check</h3>";
$csv_file = 'db_gcs_links.csv';
echo "CSV file path: " . $csv_file . "<br>";
echo "Full path: " . realpath($csv_file) . "<br>";
echo "Current directory: " . getcwd() . "<br>";
echo "File exists: " . (file_exists($csv_file) ? '✅ YES' : '❌ NO') . "<br>";
echo "File readable: " . (is_readable($csv_file) ? '✅ YES' : '❌ NO') . "<br>";
echo "File writable: " . (is_writable($csv_file) ? '✅ YES' : '❌ NO') . "<br>";
echo "Directory writable: " . (is_writable('.') ? '✅ YES' : '❌ NO') . "<br>";

// Test 2: Try to read current file contents
echo "<h3>📄 Current File Contents</h3>";
if (file_exists($csv_file)) {
    $contents = file_get_contents($csv_file);
    echo "File size: " . strlen($contents) . " bytes<br>";
    echo "Content preview:<br>";
    echo "<pre>" . htmlspecialchars($contents) . "</pre>";
} else {
    echo "❌ File does not exist<br>";
}

// Test 3: Try to write a test record
echo "<h3>✍️ Write Test</h3>";
try {
    $test_record = [
        time(),
        'test-file.txt',
        'test-uploaded-file.txt',
        'test-bucket',
        1024,
        '1.00 KB',
        date('c'),
        'https://test-download-url.com',
        'https://test-public-url.com',
        'Active'
    ];
    
    $fp = fopen($csv_file, 'a');
    if ($fp) {
        $result = fputcsv($fp, $test_record);
        fclose($fp);
        
        if ($result) {
            echo "✅ Test record written successfully (" . $result . " bytes)<br>";
        } else {
            echo "❌ Failed to write test record<br>";
        }
    } else {
        echo "❌ Could not open file for writing<br>";
    }
} catch (Exception $e) {
    echo "❌ Exception: " . $e->getMessage() . "<br>";
}

// Test 4: Verify the write
echo "<h3>🔍 Verification</h3>";
if (file_exists($csv_file)) {
    $contents = file_get_contents($csv_file);
    echo "File size after test: " . strlen($contents) . " bytes<br>";
    echo "Last 200 characters:<br>";
    echo "<pre>" . htmlspecialchars(substr($contents, -200)) . "</pre>";
}

// Test 5: Test JSON POST simulation
echo "<h3>📡 POST Request Simulation</h3>";
$test_data = [
    'originalFileName' => 'simulation-test.pdf',
    'uploadedFileName' => 'sim_' . time() . '_test.pdf',
    'bucket' => 'test-bucket',
    'size' => 2048,
    'uploadDate' => date('c'),
    'downloadUrl' => 'https://storage.cloud.google.com/test-bucket/sim_test.pdf',
    'publicUrl' => 'https://storage.googleapis.com/test-bucket/sim_test.pdf'
];

echo "Test data to be sent:<br>";
echo "<pre>" . json_encode($test_data, JSON_PRETTY_PRINT) . "</pre>";

// Simulate the save_to_database.php logic
try {
    $csv_row = [
        time(),
        $test_data['originalFileName'],
        $test_data['uploadedFileName'],
        $test_data['bucket'],
        $test_data['size'],
        number_format($test_data['size'] / 1024, 2) . ' KB',
        $test_data['uploadDate'],
        $test_data['downloadUrl'],
        $test_data['publicUrl'],
        'Active'
    ];
    
    $fp = fopen($csv_file, 'a');
    if ($fp) {
        $result = fputcsv($fp, $csv_row);
        fclose($fp);
        
        if ($result) {
            echo "✅ Simulation record written successfully<br>";
        } else {
            echo "❌ Failed to write simulation record<br>";
        }
    } else {
        echo "❌ Could not open file for simulation write<br>";
    }
} catch (Exception $e) {
    echo "❌ Simulation exception: " . $e->getMessage() . "<br>";
}

echo "<h3>🎯 Final File State</h3>";
if (file_exists($csv_file)) {
    $lines = file($csv_file);
    echo "Total lines in file: " . count($lines) . "<br>";
    echo "File contents:<br>";
    echo "<pre>" . htmlspecialchars(file_get_contents($csv_file)) . "</pre>";
}

echo "<hr>";
echo "<p><strong>Instructions:</strong></p>";
echo "<ol>";
echo "<li>Run this test by visiting: <code>http://localhost/testfiles/html_tests/test_database.php</code></li>";
echo "<li>Check if all tests show ✅ (green checkmarks)</li>";
echo "<li>If any show ❌, that indicates the problem area</li>";
echo "<li>After running this test, try uploading a file through the HTML page</li>";
echo "</ol>";
?>
