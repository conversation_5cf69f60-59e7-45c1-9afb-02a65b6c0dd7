<?php
// test_connection.php - Test database connection
echo "<h2>Testing PostgreSQL Connection</h2>";

$host = '************';
$port = '5432';
$dbname = 'postgres';
$username = 'postgres';
$password = 'Da<PERSON><PERSON>@2025';

echo "<p><strong>Connection Details:</strong></p>";
echo "<ul>";
echo "<li>Host: $host</li>";
echo "<li>Port: $port</li>";
echo "<li>Database: $dbname</li>";
echo "<li>Username: $username</li>";
echo "<li>Password: " . str_repeat('*', strlen($password)) . "</li>";
echo "</ul>";

try {
    echo "<p>Attempting to connect...</p>";
    
    $pdo = new PDO("pgsql:host=$host;port=$port;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    
    echo "<p style='color: green;'><strong>✅ Connection successful!</strong></p>";
    
    // Test a simple query
    $stmt = $pdo->query("SELECT version()");
    $version = $stmt->fetchColumn();
    echo "<p><strong>PostgreSQL Version:</strong> $version</p>";
    
    // Check if users table exists
    $stmt = $pdo->query("SELECT to_regclass('users')");
    $table_exists = $stmt->fetchColumn();
    
    if ($table_exists) {
        echo "<p style='color: green;'>✅ Users table exists</p>";
        
        // Count users
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $count = $stmt->fetchColumn();
        echo "<p>Users in table: $count</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Users table does not exist. Run init.php to create it.</p>";
    }
    
} catch(PDOException $e) {
    echo "<p style='color: red;'><strong>❌ Connection failed:</strong></p>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    
    echo "<h3>Troubleshooting Tips:</h3>";
    echo "<ul>";
    echo "<li>Check if your IP address is allowed in Google Cloud SQL</li>";
    echo "<li>Verify the password is correct</li>";
    echo "<li>Make sure the PostgreSQL instance is running</li>";
    echo "<li>Check if SSL is required (try adding sslmode=require to connection string)</li>";
    echo "</ul>";
}
?>
